import { NextRequest, NextResponse } from 'next/server';
import { getWebhookConfigForPath, getPathFromReferer } from '@/utils/webhookConfig';
import { getConfigFilePathForPath, getConfigDirForPath, getPathFromHeaders } from '@/utils/pathConfig';

export async function POST(request: NextRequest) {
  try {
    // Extract path from URL query parameters - look for 'u' parameter
    const url = new URL(request.url);
    const userParam = url.searchParams.get('u');
    const tokenParam = url.searchParams.get('token');

    let refererPath = '/';
    if (userParam) {
      refererPath = `/${userParam}`;
      console.log(`🎯 Fast-proxy: Using path from URL parameter: ${refererPath}`);
    } else {
      // Fallback to headers if no 'u' parameter
      refererPath = getPathFromHeaders(request);
      console.log(`🎯 Fast-proxy: Using path from headers: ${refererPath}`);
    }

    const webhookConfig = await getWebhookConfigForPath(refererPath);
    const WEBHOOK_URL = `${webhookConfig.webhookHost}/webhook_instant`;

    if (!WEBHOOK_URL) {
      return NextResponse.json({ error: 'Webhook not configured' }, { status: 500 });
    }

    // Get JSON body for forwarding
    const body = await request.json();
    const accessToken = webhookConfig.webhookAccessToken;
    
    if (accessToken != tokenParam) {
      return NextResponse.json({ error: 'Webhook token mismatch' }, { status: 500 });
    }
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'NextJS-Webhook-Proxy/1.0',
    };
    // Add access token to headers if available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
      headers['X-Access-Token'] = accessToken;
    }

    // Fast forward - minimal headers, no logging
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(body),
    });

    // Return response directly
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });

  } catch (error) {
    console.error('❌ Fast-proxy error:', error);
    return NextResponse.json({
      error: 'Request failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  // Extract path from URL query parameters for GET requests too
  const url = new URL(request.url);
  const userParam = url.searchParams.get('u');
  const tokenParam = url.searchParams.get('token');

  let refererPath = '/';
  if (userParam) {
    refererPath = `/${userParam}`;
  } else {
    refererPath = getPathFromHeaders(request);
  }

  const webhookConfig = await getWebhookConfigForPath(refererPath);
  const WEBHOOK_URL = `${webhookConfig.webhookHost}/webhook_instant`;
  const accessToken = webhookConfig.webhookAccessToken;
  
  if (accessToken != tokenParam) {
    return NextResponse.json({ error: 'Webhook token mismatch' }, { status: 500 });
  }
  
  return NextResponse.json({
    message: 'Fast JSON proxy - POST only',
    target: WEBHOOK_URL,
    path: refererPath,
    userParam: userParam
  });
}
