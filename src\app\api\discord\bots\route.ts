// app/api/discord/bots/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { DiscordBotConfig } from '@/types/discord';
import { getConfigFilePathForPath, getConfigDirForPath, getPathFromHeaders } from '@/utils/pathConfig';
// Import discordService only when needed to avoid build issues
// import { discordService } from '@/services/discordService';

const CONFIG_DIR = path.join(process.cwd(), 'config');
const LEGACY_BOTS_CONFIG_FILE = path.join(process.cwd(), 'discord-bots.json');

// Ensure config directory exists for a specific path
async function ensureConfigDir(configDir: string) {
  try {
    await fs.access(configDir);
  } catch {
    await fs.mkdir(configDir, { recursive: true });
  }
}

async function loadBots(refererPath: string): Promise<DiscordBotConfig[]> {
  try {
    const configDir = getConfigDirForPath(refererPath);
    const botsFile = getConfigFilePathForPath(refererPath, 'discord-bots.json');

    await ensureConfigDir(configDir);

    // Try to load from path-specific config folder
    try {
      const data = await fs.readFile(botsFile, 'utf8');
      console.log(`Loaded Discord bots from: ${botsFile}`);
      return JSON.parse(data);
    } catch {
      // Return empty array for new path-specific configs
      console.log(`No Discord bots config found at: ${botsFile}, returning empty array`);
      return [];
    }
  } catch (error) {
    console.error(`Failed to load Discord bots for path ${refererPath}:`, error);
    return [];
  }
}

async function saveBots(bots: DiscordBotConfig[], refererPath: string): Promise<void> {
  const configDir = getConfigDirForPath(refererPath);
  const botsFile = getConfigFilePathForPath(refererPath, 'discord-bots.json');

  await ensureConfigDir(configDir);
  await fs.writeFile(botsFile, JSON.stringify(bots, null, 2));
  console.log(`Saved Discord bots to: ${botsFile}`);
}

export async function GET(request: NextRequest) {
  try {
    const refererPath = getPathFromHeaders(request);
    console.log(`Loading Discord bots for path: ${refererPath}`);

    const bots = await loadBots(refererPath);
    // For now, just return bots without runtime status to avoid build issues
    // TODO: Add runtime status check when discord service is properly initialized
    return NextResponse.json(bots);
  } catch (error) {
    console.error('Failed to load Discord bots:', error);
    return NextResponse.json({ error: 'Failed to load bots' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const refererPath = getPathFromHeaders(request);
    console.log(`Saving Discord bots for path: ${refererPath}`);

    const bots: DiscordBotConfig[] = await request.json();
    await saveBots(bots, refererPath);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save Discord bots:', error);
    return NextResponse.json({ error: 'Failed to save bots' }, { status: 500 });
  }
}
