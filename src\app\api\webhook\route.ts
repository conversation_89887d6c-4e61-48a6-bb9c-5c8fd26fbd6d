import { NextRequest, NextResponse } from 'next/server';
import { getWebhookConfigForPath, getPathFromReferer, buildWebhookUrls } from '@/utils/webhookConfig';
import { getPathFromHeaders } from '@/utils/pathConfig';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { group, data } = body;

    // Get the current path from headers
    const refererPath = getPathFromHeaders(request);

    // Log the webhook call for debugging
    console.log(`🔗 Webhook called for path: ${refererPath}`);

    const webhookConfig = await getWebhookConfigForPath(refererPath);

    // Build webhook URLs with the path-specific host
    const WEBHOOK_URLS = {
      g_instant: `${webhookConfig.webhookHost}/webhook_instant`,
      g_input: `${webhookConfig.webhookHost}/webhook_input`,
      g_control: `${webhookConfig.webhookHost}/webhook_control`,
    };

    if (!group || !WEBHOOK_URLS[group as keyof typeof WEBHOOK_URLS]) {
      return NextResponse.json(
        { error: true, message: 'Invalid group specified' },
        { status: 400 }
      );
    }

    const webhookUrl = WEBHOOK_URLS[group as keyof typeof WEBHOOK_URLS];

    if (!webhookUrl) {
      return NextResponse.json(
        { error: true, message: 'Webhook URL not configured' },
        { status: 500 }
      );
    }

    console.log(`Proxying webhook to ${group}: ${webhookUrl} (path: ${refererPath})`);
    console.log('Data:', JSON.stringify(data, null, 2));

    // Get access token from path-specific configuration
    const accessToken = webhookConfig.webhookAccessToken;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'NextJS-Webhook-Proxy/1.0',
    };

    // Add access token to headers if available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
      headers['X-Access-Token'] = accessToken;
    }

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        ...data,
        // Add metadata
        _meta: {
          source: 'web_app',
          group,
          path: refererPath,
          webhookHost: webhookConfig.webhookHost,
          timestamp: new Date().toISOString(),
          accessToken: accessToken ? 'provided' : 'not_provided'
        }
      }),
    });

    const responseText = await response.text();
    let responseData;

    // Log the raw response for debugging
    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));
    console.log(`Raw response:`, responseText.substring(0, 500)); // First 500 chars

    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = {
        message: responseText,
        isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html')
      };
    }

    // If the webhook server returned an error status, mark as unsuccessful
    const success = response.ok && !responseText.includes('Internal Server Error');

    return NextResponse.json({
      success,
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      webhookUrl, // Include for debugging
    });

  } catch (error: unknown) {
    console.error('Webhook proxy error:', error);
    return NextResponse.json(
      { 
        error: true, 
        message: (error as Error).message || 'Internal server error',
        details: String(error)
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Access-Token',
    },
  });
}
