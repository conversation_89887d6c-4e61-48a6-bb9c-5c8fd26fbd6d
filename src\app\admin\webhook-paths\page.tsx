'use client';

import WebhookPathManager from '@/components/WebhookPathManager';

export default function WebhookPathsAdminPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-6xl mx-auto px-6 py-4">
          <h1 className="text-3xl font-bold text-gray-900">Webhook Path Administration</h1>
          <p className="text-gray-600 mt-2">
            Manage webhook configurations for different URL paths (/zd, /bb, etc.)
          </p>
        </div>
      </div>
      
      <div className="max-w-6xl mx-auto py-6">
        <WebhookPathManager />
      </div>
      
      <div className="max-w-6xl mx-auto px-6 py-4">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-semibold text-blue-900 mb-2">How it works:</h3>
          <ul className="text-blue-800 text-sm space-y-1">
            <li>• Each URL path (/zd, /bb, etc.) can have its own webhook host and access token</li>
            <li>• API calls from different paths will use their respective webhook configurations</li>
            <li>• The system automatically detects the current path and applies the correct settings</li>
            <li>• Fallback to default configuration if no specific path configuration is found</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
