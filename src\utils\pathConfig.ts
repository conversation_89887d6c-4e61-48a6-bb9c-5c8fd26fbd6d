// utils/pathConfig.ts
import path from 'path';
import { PATH_CONFIG } from '@/config/global';

/**
 * Get the configuration directory path for a given request path
 * @param requestPath - The request path (e.g., "/zd", "/bb", "/")
 * @returns The configuration directory path
 */
export function getConfigDirForPath(requestPath: string): string {
  const CONFIG_BASE_DIR = path.join(process.cwd(), 'config');
  
  // Extract the first segment of the path
  const pathSegments = requestPath.split('/').filter(Boolean);
  const firstSegment = pathSegments[0];
  
  // If we have a recognized path segment, use its specific config directory
  // if (firstSegment && ['zd', 'bb'].includes(firstSegment)) {
  if (firstSegment && PATH_CONFIG[firstSegment]) {
    return path.join(CONFIG_BASE_DIR, firstSegment);
  }
  
  // For root path or unrecognized paths, throw an error
  throw new Error(`No configuration directory found for path: ${requestPath}. Supported paths: /zd, /bb`);
}

/**
 * Get the full path to a specific config file for a given request path
 * @param requestPath - The request path (e.g., "/zd", "/bb")
 * @param configFileName - The config file name (e.g., "app-settings.json")
 * @returns The full path to the config file
 */
export function getConfigFilePathForPath(requestPath: string, configFileName: string): string {
  const configDir = getConfigDirForPath(requestPath);
  return path.join(configDir, configFileName);
}

/**
 * Extract the current path from request headers
 * Tries multiple sources in priority order:
 * 1. x-current-path (sent by frontend)
 * 2. x-referer-path (set by middleware)
 * 3. referer header (parsed directly)
 * @param request - The NextRequest object
 * @returns The extracted path or '/' as fallback
 */
export function getPathFromHeaders(request: Request): string {
  // First try the x-current-path header (sent directly by frontend components)
  const currentPath = request.headers.get('x-current-path');
  if (currentPath) {
    return currentPath;
  }

  // Fallback to x-referer-path (set by middleware)
  const middlewarePath = request.headers.get('x-referer-path');
  if (middlewarePath) {
    return middlewarePath;
  }

  // Last resort: parse referer header directly
  const referer = request.headers.get('referer');
  if (referer) {
    try {
      const refererUrl = new URL(referer);
      return refererUrl.pathname;
    } catch {
      return '/';
    }
  }

  return '/';
}
