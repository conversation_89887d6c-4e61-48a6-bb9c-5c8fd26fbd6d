"use client";
import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';

const LoginScreen: React.FC = () => {
  const [pin, setPin] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      // Small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 300));

      const success = await login(pin);
      if (!success) {
        setError("Incorrect PIN. Please try again.");
        setPin("");
      }
    } catch (error) {
      console.error('Login error:', error);
      setError("Authentication failed. Please try again.");
      setPin("");
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSubmit(e as any);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-blue-900 to-gray-900">
      <div className="bg-gray-800/90 backdrop-blur-sm p-8 rounded-xl shadow-2xl max-w-md w-full mx-4 border border-gray-700">
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🔐</div>
          <h1 className="text-3xl font-bold text-white mb-2">Secure Access</h1>
          <p className="text-gray-400">Enter your PIN to access the application</p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <input
              type="password"
              placeholder="Enter PIN"
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-full px-4 py-4 bg-gray-700/50 text-white rounded-lg border border-gray-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none text-center text-xl tracking-[0.5em] font-mono"
              maxLength={6}
              autoFocus
              disabled={isLoading}
            />
          </div>
          
          {error && (
            <div className="text-red-400 text-sm text-center bg-red-900/20 border border-red-800/30 p-3 rounded-lg">
              <span className="text-red-300">❌</span> {error}
            </div>
          )}
          
          <button
            type="submit"
            disabled={isLoading || pin.length === 0}
            className="w-full py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-[1.02] active:scale-[0.98]"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Authenticating...
              </div>
            ) : (
              <>🚀 Access Application</>
            )}
          </button>
        </form>
        
        <div className="mt-8 text-center">
          <div className="text-xs text-gray-500 bg-gray-900/30 p-3 rounded-lg border border-gray-700/50">
            <div className="flex items-center justify-center mb-2">
              <span className="text-green-400">🛡️</span>
              <span className="ml-2 font-medium">Session Security</span>
            </div>
            <p>Your session will be remembered until you close the browser</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginScreen;
