// middleware.ts
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // Get the current path
  const pathname = request.nextUrl.pathname;
  
  // Add the current path to headers so API routes can access it
  response.headers.set('x-current-path', pathname);
  
  // Also add it to the request headers for API routes
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-current-path', pathname);
  
  // For API routes, we need to pass the path information
  if (pathname.startsWith('/api/')) {
    // First check if the frontend provided the current path directly
    const frontendPath = request.headers.get('x-current-path');

    if (frontendPath) {
      requestHeaders.set('x-referer-path', frontendPath);
    } else {
      // Fallback to referer to determine which path the API call is coming from
      const referer = request.headers.get('referer');
      if (referer) {
        try {
          const refererUrl = new URL(referer);
          const refererPath = refererUrl.pathname;
          requestHeaders.set('x-referer-path', refererPath);
        } catch {
          // If referer parsing fails, use root path
          requestHeaders.set('x-referer-path', '/');
        }
      } else {
        requestHeaders.set('x-referer-path', '/');
      }
    }
    
    return NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });
  }
  
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
