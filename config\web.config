
<configuration>
  <system.webServer>
    <rewrite>
      <rules> 
        <rule name="t-Rule" stopProcessing="true">
            <match url=".*" />
            <conditions>
                        <add input="{HTTP_HOST}" pattern="^t\.edutest\.space$" />
            </conditions>
            <action type="Rewrite" url="http://localhost:3002/{R:0}" />
        </rule>
                <rule name="Imported Rule 1" stopProcessing="true">
                    <match url="^" ignoreCase="false" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
                        <add input="{URL}" pattern="(.+)/$" ignoreCase="false" />
                    </conditions>
                    <action type="Redirect" url="{C:1}" redirectType="Permanent" />
                </rule>
                <rule name="Imported Rule 2" stopProcessing="true">
                    <match url="^" ignoreCase="false" />
                    <conditions logicalGrouping="MatchAll">
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php" />
                </rule>
      </rules>
    </rewrite>
    <defaultDocument>
        <files>
            <add value="index.php" />
            <remove value="default.aspx" />
            <remove value="iisstart.htm" />
            <remove value="index.html" />
            <remove value="index.htm" />
            <remove value="Default.asp" />
            <remove value="Default.htm" />
        </files>
    </defaultDocument>
  </system.webServer>
</configuration>
