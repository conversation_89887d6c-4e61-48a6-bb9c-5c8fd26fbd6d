[{"id": "1765427703212", "name": "Test1", "enabled": false, "aiProvider": "gpt", "webhookUrl": "", "autoPlaceOrder": false, "scheduleCheck": false, "checkInterval": 60, "symbol": "XAUUSD", "timeframes": ["M15", "H1", "H4"], "barback": 50, "additionalPrompt": "Focus on current market session timing, recent news impact, and provide conservative risk management. Consider if this is a good time to enter based on volatility and market conditions.", "createdAt": "2025-12-11T04:35:03.212Z"}]