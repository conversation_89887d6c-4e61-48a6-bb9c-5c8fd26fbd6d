// contexts/AppSettingsContext.tsx
"use client";
import { createContext, useContext, useState, useEffect, useRef, ReactNode } from 'react';

interface AppSettings {
  showTabNavigation: boolean;
  requireConfirmation: boolean;
  magicNumber: number;
  visibleTabs: {
    instant: boolean;
    input: boolean;
    control: boolean;
    calendar: boolean;
    discord: boolean;
    aibot: boolean;
    orders: boolean;
  };
  currency: {
    showThb: boolean;
    usdToThbRate: number;
  };
  orders: {
    showCloseColumn: boolean;
  };
}

interface AppSettingsContextType {
  settings: AppSettings;
  updateSettings: (newSettings: Partial<AppSettings>) => void;
  toggleTabVisibility: (tab: keyof AppSettings['visibleTabs']) => void;
  toggleTabNavigation: () => void;
  toggleConfirmation: () => void;
  updateMagicNumber: (value: number) => void;
  toggleThbDisplay: () => void;
  updateExchangeRate: (rate: number) => void;
  toggleCloseColumn: () => void;
}

const defaultSettings: AppSettings = {
  showTabNavigation: true,
  requireConfirmation: true,
  magicNumber: 50,
  visibleTabs: {
    instant: true,
    input: true,
    control: true,
    calendar: false,
    discord: false,
    aibot: true,
    orders: true,
  },
  currency: {
    showThb: true,
    usdToThbRate: 32.5,
  },
  orders: {
    showCloseColumn: true,
  },
};

const AppSettingsContext = createContext<AppSettingsContextType | undefined>(undefined);

export function AppSettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AppSettings>(defaultSettings);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isSavingRef = useRef(false);

  // Debug: Log when provider mounts/unmounts
  useEffect(() => {
    console.log('AppSettingsProvider mounted');
    return () => {
      console.log('AppSettingsProvider unmounted');
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, []);

  // Load settings from config file on mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Try to load from config file first
        const response = await fetch('/api/config/app-settings');
        if (response.ok) {
          const parsed = await response.json();
          setSettings({ ...defaultSettings, ...parsed });
        } else {
          // Fallback to localStorage for backward compatibility
          const savedSettings = localStorage.getItem('app-settings');
          if (savedSettings) {
            const parsed = JSON.parse(savedSettings);
            setSettings({ ...defaultSettings, ...parsed });
            // Migrate to config file
            await saveSettingsToFile({ ...defaultSettings, ...parsed });
          }
        }
      } catch (error) {
        console.error('Failed to load app settings:', error);
        // Fallback to localStorage
        try {
          const savedSettings = localStorage.getItem('app-settings');
          if (savedSettings) {
            const parsed = JSON.parse(savedSettings);
            setSettings({ ...defaultSettings, ...parsed });
          }
        } catch (localError) {
          console.error('Failed to load from localStorage:', localError);
        }
      } finally {
        // Mark initial load as complete
        setIsInitialLoad(false);
      }
    };

    loadSettings();
  }, []);

  // Save settings to config file whenever they change (but not during initial load)
  // Use debouncing to prevent rapid successive saves
  useEffect(() => {
    if (!isInitialLoad && settings && typeof settings === 'object' && settings.visibleTabs) {
      console.log('Settings changed, scheduling save:', settings);

      // Clear any existing timeout
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      // Set a new timeout to save after 500ms of no changes
      saveTimeoutRef.current = setTimeout(() => {
        console.log('Debounced save executing');
        saveSettingsToFile(settings);
      }, 500);
    }

    // Cleanup timeout on unmount
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [settings, isInitialLoad]);

  const saveSettingsToFile = async (settingsToSave: AppSettings) => {
    // Prevent multiple simultaneous saves
    if (isSavingRef.current) {
      console.log('Save already in progress, skipping');
      return;
    }

    isSavingRef.current = true;

    try {
      console.log('Saving settings to file:', settingsToSave);
      const jsonBody = JSON.stringify(settingsToSave);
      console.log('JSON body length:', jsonBody.length);

      // Validate JSON before sending
      if (!jsonBody || jsonBody.length === 0) {
        console.error('Empty JSON body, skipping save');
        return;
      }

      const response = await fetch('/api/config/app-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonBody,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Save settings API error:', response.status, errorText);
      } else {
        console.log('Settings saved successfully');
      }

      // Also save to localStorage as backup
      localStorage.setItem('app-settings', JSON.stringify(settingsToSave));
    } catch (error) {
      console.error('Failed to save app settings to file:', error);
      // Fallback to localStorage only
      try {
        localStorage.setItem('app-settings', JSON.stringify(settingsToSave));
      } catch (localError) {
        console.error('Failed to save to localStorage:', localError);
      }
    } finally {
      isSavingRef.current = false;
    }
  };

  const updateSettings = (newSettings: Partial<AppSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const toggleTabVisibility = (tab: keyof AppSettings['visibleTabs']) => {
    setSettings(prev => ({
      ...prev,
      visibleTabs: {
        ...prev.visibleTabs,
        [tab]: !prev.visibleTabs[tab],
      },
    }));
  };

  const toggleTabNavigation = () => {
    setSettings(prev => ({
      ...prev,
      showTabNavigation: !prev.showTabNavigation,
    }));
  };

  const toggleConfirmation = () => {
    setSettings(prev => ({
      ...prev,
      requireConfirmation: !prev.requireConfirmation,
    }));
  };

  const updateMagicNumber = (value: number) => {
    setSettings(prev => ({
      ...prev,
      magicNumber: value,
    }));
  };

  const toggleThbDisplay = () => {
    setSettings(prev => ({
      ...prev,
      currency: {
        ...prev.currency,
        showThb: !prev.currency.showThb,
      },
    }));
  };

  const updateExchangeRate = (rate: number) => {
    setSettings(prev => ({
      ...prev,
      currency: {
        ...prev.currency,
        usdToThbRate: rate,
      },
    }));
  };

  const toggleCloseColumn = () => {
    setSettings(prev => ({
      ...prev,
      orders: {
        ...prev.orders,
        showCloseColumn: !prev.orders.showCloseColumn,
      },
    }));
  };

  return (
    <AppSettingsContext.Provider value={{
      settings,
      updateSettings,
      toggleTabVisibility,
      toggleTabNavigation,
      toggleConfirmation,
      updateMagicNumber,
      toggleThbDisplay,
      updateExchangeRate,
      toggleCloseColumn,
    }}>
      {children}
    </AppSettingsContext.Provider>
  );
}

export function useAppSettings() {
  const context = useContext(AppSettingsContext);
  if (context === undefined) {
    throw new Error('useAppSettings must be used within an AppSettingsProvider');
  }
  return context;
}
