import { NextRequest, NextResponse } from 'next/server';
import { getPathFromHeaders } from '@/utils/pathConfig';
import { PATH_CONFIG } from '@/config/global';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    // Get the current path from headers
    const refererPath = getPathFromHeaders(request).replace('/', '');
    console.log(`PIN API called for path: ${refererPath}, action: ${action}`);

    if (action === 'get_pin') {
      
      // Get path-specific PIN 
      const pin = PATH_CONFIG[refererPath]?.pin;
      
      if (!pin) {
        return NextResponse.json(
          { 
            error: true, 
            message: `No PIN configured for path: ${refererPath}. Please configure PIN_${refererPath.replace('/', '').toUpperCase()} in environment variables.` 
          },
          { status: 404 }
        );
      }

      return NextResponse.json({ 
        success: true, 
        pin,
        path: refererPath 
      });
    }

    return NextResponse.json(
      { error: true, message: 'Invalid action' },
      { status: 400 }
    );

  } catch (error: unknown) {
    console.error('PIN API error:', error);
    return NextResponse.json(
      { 
        error: true, 
        message: (error as Error).message || 'Internal server error',
        details: String(error)
      },
      { status: 500 }
    );
  }
}
