import { NextRequest, NextResponse } from 'next/server';
import { getWebhookConfigForPath, getPathFromReferer } from '@/utils/webhookConfig';

export async function GET(request: NextRequest) {
  return handleProxy(request, 'GET');
}

export async function POST(request: NextRequest) {
  return handleProxy(request, 'POST');
}

export async function PUT(request: NextRequest) {
  return handleProxy(request, 'PUT');
}

export async function DELETE(request: NextRequest) {
  return handleProxy(request, 'DELETE');
}

export async function PATCH(request: NextRequest) {
  return handleProxy(request, 'PATCH');
}

async function handleProxy(request: NextRequest, method: string) {
  try {
    // Extract path from URL query parameters - look for 'u' parameter
    const url = new URL(request.url);
    const userParam = url.searchParams.get('u');
    const tokenParam = url.searchParams.get('token');

    let refererPath = '/';
    if (userParam) {
      refererPath = `/${userParam}`;
      console.log(`🎯 Proxy: Using path from URL parameter: ${refererPath}`);
    } else {
      // Fallback to headers if no 'u' parameter
      refererPath = request.headers.get('x-referer-path') || '/';
      console.log(`🎯 Proxy: Using path from headers: ${refererPath}`);
    }

    // Get the request body if it exists
    let body = null;
    const contentType = request.headers.get('content-type');

    const webhookConfig = await getWebhookConfigForPath(refererPath);
    const WEBHOOK_URL = `${webhookConfig.webhookHost}/webhook_instant`; 
    const accessToken = webhookConfig.webhookAccessToken;
    
    if (accessToken != tokenParam) {
      return NextResponse.json({ error: 'Webhook token mismatch' }, { status: 500 });
    }
    
    if (!WEBHOOK_URL) {
      return NextResponse.json(
        { error: 'Webhook URL not configured' },
        { status: 500 }
      );
    }

    console.log(`🔄 Proxying ${method} request to:`, WEBHOOK_URL);

    // Handle body for non-GET/DELETE requests (if not already processed for JSON path extraction)
    if (method !== 'GET' && method !== 'DELETE') {
      if (contentType?.includes('application/json')) {
        // Body already processed for path extraction if it was JSON
        if (body === null) {
          body = await request.json();
        }
        console.log('📤 Forwarding JSON body:', JSON.stringify(body, null, 2));
      } else if (contentType?.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData();
        body = Object.fromEntries(formData);
        console.log('📤 Forwarding form data:', body);
      } else if (contentType?.includes('text/')) {
        body = await request.text();
        console.log('📤 Forwarding text body:', body);
      } else {
        // For other content types, get as buffer
        const buffer = await request.arrayBuffer();
        body = buffer;
        console.log('📤 Forwarding binary data, size:', buffer.byteLength, 'bytes');
      }
    }

    // Get query parameters from the original request (reuse the url variable)
    const searchParams = url.searchParams;

    // Remove the 'u' parameter from forwarded query params since it's for routing
    const forwardParams = new URLSearchParams();
    searchParams.forEach((value, key) => {
      if (key !== 'u') {
        forwardParams.append(key, value);
      }
    });

    const targetUrl = forwardParams.toString()
      ? `${WEBHOOK_URL}?${forwardParams.toString()}`
      : WEBHOOK_URL;

    console.log('🎯 Target URL:', targetUrl);

    // Prepare headers (exclude some that shouldn't be forwarded)
    const forwardHeaders = new Headers();
    request.headers.forEach((value, key) => {
      // Skip headers that shouldn't be forwarded
      if (!['host', 'connection', 'content-length'].includes(key.toLowerCase())) {
        forwardHeaders.set(key, value);
      }
    });

    // Make the request to the target webhook
    const response = await fetch(targetUrl, {
      method: method,
      headers: forwardHeaders,
      body: body ? (
        contentType?.includes('application/json') ? JSON.stringify(body) :
        contentType?.includes('application/x-www-form-urlencoded') ? new URLSearchParams(body).toString() :
        body
      ) : undefined,
    });

    console.log(`📥 Response status: ${response.status} ${response.statusText}`);

    // Get response data
    const responseContentType = response.headers.get('content-type');
    let responseData;

    if (responseContentType?.includes('application/json')) {
      responseData = await response.json();
      console.log('📥 Response JSON:', JSON.stringify(responseData, null, 2));
    } else if (responseContentType?.includes('text/')) {
      responseData = await response.text();
      console.log('📥 Response text:', responseData);
    } else {
      // For binary data, convert to base64
      const buffer = await response.arrayBuffer();
      responseData = Buffer.from(buffer).toString('base64');
      console.log('📥 Response binary data, size:', buffer.byteLength, 'bytes');
    }

    // Forward the response with the same status and headers
    const responseHeaders = new Headers();
    response.headers.forEach((value, key) => {
      responseHeaders.set(key, value);
    });

    return new NextResponse(
      responseContentType?.includes('application/json') ? JSON.stringify(responseData) : responseData,
      {
        status: response.status,
        statusText: response.statusText,
        headers: responseHeaders,
      }
    );

  } catch (error) {
    console.error('❌ Proxy error:', error);
    return NextResponse.json(
      {
        error: 'Proxy request failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
