// utils/webhookConfig.ts
import { promises as fs } from 'fs';
import path from 'path';
import { WebhookPathConfig } from '@/app/api/config/webhook-paths/route';

const CONFIG_DIR = path.join(process.cwd(), 'config');
const WEBHOOK_PATHS_CONFIG_FILE = path.join(CONFIG_DIR, 'webhook-paths.json');

let cachedConfigs: WebhookPathConfig[] = [];
let lastCacheTime = 0;
const CACHE_DURATION = 30000; // 30 seconds

async function loadWebhookPathConfigs(): Promise<WebhookPathConfig[]> {
  const now = Date.now();
  
  // Return cached configs if still valid
  if (cachedConfigs.length > 0 && (now - lastCacheTime) < CACHE_DURATION) {
    return cachedConfigs;
  }

  try {
    const data = await fs.readFile(WEBHOOK_PATHS_CONFIG_FILE, 'utf8');
    cachedConfigs = JSON.parse(data);
    lastCacheTime = now;
    return cachedConfigs;
  } catch (error) {
    console.error('Failed to load webhook path configurations:', error);
    // Return empty array if file doesn't exist - no default fallback
    cachedConfigs = [];
    lastCacheTime = now;
    return [];
  }
}

export async function getWebhookConfigForPath(requestPath: string): Promise<WebhookPathConfig> {
  const configs = await loadWebhookPathConfigs();

  console.log(`Looking for webhook config for path: ${requestPath}`);
  console.log(`Available configs:`, configs.map(c => ({ path: c.path, enabled: c.enabled, id: c.id })));

  // Find exact path match first
  let matchedConfig = configs.find(config =>
    config.enabled && config.path === requestPath
  );

  // If no exact match, find the longest matching path prefix
  if (!matchedConfig) {
    const sortedConfigs = configs
      .filter(config => config.enabled && requestPath.startsWith(config.path))
      .sort((a, b) => b.path.length - a.path.length);

    matchedConfig = sortedConfigs[0];
  }

  console.log(`Matched config:`, matchedConfig ? { path: matchedConfig.path, id: matchedConfig.id, host: matchedConfig.webhookHost } : 'none');

  // If no config found, throw an error instead of falling back
  if (!matchedConfig) {
    throw new Error(`No webhook configuration found for path: ${requestPath}. Please configure webhook settings for this path.`);
  }

  return matchedConfig;
}

export function getPathFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.pathname;
  } catch {
    return "/";
  }
}

export function getPathFromReferer(referer: string | null): string {
  if (!referer) return "/";
  
  try {
    const urlObj = new URL(referer);
    return urlObj.pathname;
  } catch {
    return "/";
  }
}

// Clear cache function for testing or manual refresh
export function clearWebhookConfigCache(): void {
  cachedConfigs = [];
  lastCacheTime = 0;
  console.log('Webhook config cache cleared');
}

// Get webhook URLs with the configured host
export function buildWebhookUrls(webhookHost: string) {
  return {
    g_instant: `${webhookHost}/webhook_instant`,
    g_input: `${webhookHost}/webhook_input`,
    g_control: `${webhookHost}/webhook_control`,
    orders_data: `${webhookHost}/webhook_orders_data`,
    orders_action: `${webhookHost}/webhook_orders_action`,
    chart_data: `${webhookHost}/webhook_chart_data`,
    ai_analysis: `${webhookHost}/webhook_ai_analysis`,
  };
}
