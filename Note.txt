

Old test webhook https://teaching.suksapanpanit.com/extra/playground/test1.php
python port 5050 5051
nextjs port 3002
t.edutest.space 80 443

domain Rewrite t.edutest.space > http://localhost:3002/{R:0}

http://localhost:3002/{R:0} /zd call webhook http://localhost:5050

http://localhost:3002/{R:0} /bb call webhook http://localhost:5051

================================
WebHook Alert
https://t.edutest.space/api/fast-proxy
{
    "a": "Buy Limit",
    "c": "TEST_Comment",
    "p": 3282.11,
    "tp": 3312.11,
    "sl": 3272.11,
    "s": "XAUUSD"
}


================================ [IIS] Make Proxy with IIS

### 1. **IIS Site Setup**

In IIS Manager:

- Go to your existing **`domain.com`** site
- Open **Bindings** (right-hand side)
- Add a new binding:
    - Type: `http`
    - Hostname: `sub.domain.com`
    - Port: `80` (or `443` if using SSL)

> ✅ This tells IIS to accept requests for both domain.com and sub.domain.com in the same site.
> 

### 2. **Make Sure You Installed**

- ✅ **URL Rewrite**
- ✅ **Application Request Routing (ARR)**

### **3. Enable Proxy in ARR**

After installing ARR:

1. Open **IIS Manager**
2. Click on the server name in the **Connections** pane (not the website).
3. Open **Application Request Routing Cache**
4. Click on **"Server Proxy Settings..."** (right-hand Actions pane)
5. Check **"Enable proxy"**
6. Click **Apply**  

### 4. **Create URL Rewrite Rule for subdomain**

In that same site:

1. Open **URL Rewrite**
2. Add a new **Inbound Rule > Blank rule**
3. Name it something like `ProxySubdomainTo3000`

### Match URL:

- Requested URL: `Matches the Pattern`
- Pattern: `.*`

### Add Condition:

Click **Conditions** → **Add…**

- Condition Input: `{HTTP_HOST}`
- Check if input: `Matches the Pattern`
- Pattern: `^sub\.domain\.com$`

> This ensures the rule only applies when the subdomain is used. 
 

================================ Upgrade Limit

max_execution_time = 3000
max_input_time = 6000
memory_limit = 1024M   ; raise if heavy processing
default_socket_timeout = 6000

================================

setx GYP_MSVS_VERSION 2022 /M
npm install
npm run dev

================================

npm install discord.js
npm install zlib-sync bufferutil utf-8-validate


================================
============= Go Live =============
================================

npm run build
npm run start

;----- or run on background

npm install -g pm2
pm2 start npm --name "next-app" -- start
pm2 save
pm2 startup

================================
============= TEST =============
================================

🟡 PENDING 🟡


C.Ball
========================
Symbol : Xauusd
Status : Opened
Signal : Buy Limit
Price :  3332-3328
SL : 3323
TP1 : 3335
TP2 : 3344
TP3 : 3363
TP4: 3400
<@&1247398904576606270>  <@&1327952045176651807> <@&1247740639865606184>


Signal ID :  (ไม้รัน) 
C.Lew
========================
Symbol : XAUUSD.s
Status : PENDING
Signal : BUY
Price : 3309 - 3306 
SL : 3300
TP1 : 3320
TP2 : 3334
TP3 : 3345



Signal ID :  (ไม้เทส) 
C.BBB
========================
Symbol : XAUUSD.s
Status : PENDING
Signal : BUY
Price : 3209 - 3206 
SL : 3200
TP1 : 3320
TP2 : 3334
TP3 : 3345

================================
================================
================================

https://docs.google.com/forms/d/e/1FAIpQLSda4_GifbWv-MGp9j-2jdbtCYzUlDN-chjhprEMHUG4DkkH_g/viewform?usp=pp_url&entry.178506718={Symbol}&entry.645304246=Buy+Limit&entry.785671803={Entry}&entry.898028705={SL}&entry.1523790774={TP}&entry.381285031={Lot}&entry.1073635155={SignalID}&entry.1148525053={Comment}&entry.398055293={Reason}&entry.305299530={Risk}

