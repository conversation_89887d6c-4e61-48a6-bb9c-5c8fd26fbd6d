import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { getConfigFilePathForPath, getConfigDirForPath, getPathFromHeaders } from '@/utils/pathConfig';

const CONFIG_DIR = path.join(process.cwd(), 'config');

// Ensure config directory exists for a specific path
async function ensureConfigDir(configDir: string) {
  try {
    await fs.access(configDir);
  } catch {
    await fs.mkdir(configDir, { recursive: true });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get the current path from headers
    const refererPath = getPathFromHeaders(request);
    console.log(`Loading app settings for path: ${refererPath}`);

    const configDir = getConfigDirForPath(refererPath);
    const settingsFile = getConfigFilePathForPath(refererPath, 'app-settings.json');

    await ensureConfigDir(configDir);

    try {
      const data = await fs.readFile(settingsFile, 'utf8');
      const settings = JSON.parse(data);
      console.log(`Loaded settings from: ${settingsFile}`);
      return NextResponse.json(settings);
    } catch (error) {
      // File doesn't exist, return default settings
      const defaultSettings = {
        showTabNavigation: true,
        requireConfirmation: true,
        magicNumber: 50,
        visibleTabs: {
          g_instant: true,
          g_input: true,
          g_control: true,
          g_calendar: false,
          g_discord: false,
          g_aibot: true,
          g_orders: true,
        },
        currency: {
          showThb: true,
          usdToThbRate: 32.5,
        },
        orders: {
          showCloseColumn: true,
        },
      };

      // Save default settings to file
      await fs.writeFile(settingsFile, JSON.stringify(defaultSettings, null, 2));
      console.log(`Created default settings at: ${settingsFile}`);
      return NextResponse.json(defaultSettings);
    }
  } catch (error) {
    console.error('Error loading app settings:', error);
    return NextResponse.json(
      { error: 'Failed to load app settings' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the current path from headers
    const refererPath = getPathFromHeaders(request);
    console.log(`Saving app settings for path: ${refererPath}`);

    const configDir = getConfigDirForPath(refererPath);
    const settingsFile = getConfigFilePathForPath(refererPath, 'app-settings.json');

    await ensureConfigDir(configDir);

    // Check if request has body
    const contentLength = request.headers.get('content-length');
    if (!contentLength || contentLength === '0') {
      console.error('Empty request body received');
      return NextResponse.json(
        { error: 'Empty request body' },
        { status: 400 }
      );
    }

    let settings;
    try {
      settings = await request.json();
    } catch (jsonError) {
      console.error('JSON parsing error:', jsonError);
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Validate settings structure
    if (!settings || typeof settings !== 'object') {
      console.error('Invalid settings structure:', settings);
      return NextResponse.json(
        { error: 'Invalid settings format' },
        { status: 400 }
      );
    }

    // Save settings to file
    await fs.writeFile(settingsFile, JSON.stringify(settings, null, 2));
    console.log(`Saved settings to: ${settingsFile}`);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error saving app settings:', error);
    return NextResponse.json(
      { error: 'Failed to save app settings' },
      { status: 500 }
    );
  }
}
