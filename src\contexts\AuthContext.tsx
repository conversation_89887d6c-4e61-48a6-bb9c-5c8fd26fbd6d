"use client";
import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

interface AuthContextType {
  isAuthenticated: boolean;
  login: (pin: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();

  // Get path-specific PIN configuration
  const getCorrectPinForCurrentPath = async (): Promise<string | null> => {
    try {
      const response = await fetch('/api/auth/pin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-current-path': pathname
        },
        body: JSON.stringify({ action: 'get_pin' })
      });

      if (response.ok) {
        const data = await response.json();
        return data.pin;
      }
    } catch (error) {
      console.error('Failed to get PIN for path:', error);
    }
    return null;
  };

  const AUTH_KEY = `app_auth_session_${pathname.replace('/', '')}`;

  useEffect(() => {
    // Check if user is already authenticated
    const checkAuth = () => {
      try {
        const authStatus = sessionStorage.getItem(AUTH_KEY);
        const authTime = sessionStorage.getItem(AUTH_KEY + "_time");
        
        if (authStatus === "authenticated" && authTime) {
          const loginTime = parseInt(authTime);
          const currentTime = Date.now();
          const sessionDuration = 24 * 60 * 60 * 1000; // 24 hours
          
          if (currentTime - loginTime < sessionDuration) {
            setIsAuthenticated(true);
          } else {
            // Session expired
            sessionStorage.removeItem(AUTH_KEY);
            sessionStorage.removeItem(AUTH_KEY + "_time");
          }
        }
      } catch (error) {
        console.error("Auth check error:", error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async (pin: string): Promise<boolean> => {
    const correctPin = await getCorrectPinForCurrentPath();

    if (!correctPin) {
      console.error('No PIN configured for current path:', pathname);
      return false;
    }

    if (pin === correctPin) {
      setIsAuthenticated(true);
      sessionStorage.setItem(AUTH_KEY, "authenticated");
      sessionStorage.setItem(AUTH_KEY + "_time", Date.now().toString());
      return true;
    }
    return false;
  };

  const logout = () => {
    setIsAuthenticated(false);
    sessionStorage.removeItem(AUTH_KEY);
    sessionStorage.removeItem(AUTH_KEY + "_time");
  };

  const value = {
    isAuthenticated,
    login,
    logout,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
