"use client";
import React, { useState, useEffect } from 'react';

interface PartialCloseDialogProps {
  isOpen: boolean;
  position: {
    ticket: number;
    symbol: string;
    volume: number;
    type: "BUY" | "SELL";
  } | null;
  onConfirm: (volume: number) => void;
  onCancel: () => void;
}

const PartialCloseDialog: React.FC<PartialCloseDialogProps> = ({
  isOpen,
  position,
  onConfirm,
  onCancel,
}) => {
  const [percentage, setPercentage] = useState(100);
  const [volume, setVolume] = useState(0);

  // Update volume when percentage changes or position changes
  useEffect(() => {
    if (position) {
      const calculatedVolume = (position.volume * percentage) / 100;
      const finalVolume = Math.max(0.01, calculatedVolume);
      setVolume(parseFloat(finalVolume.toFixed(2)));
    }
  }, [percentage, position]);

  // Update percentage when volume changes manually
  const handleVolumeChange = (newVolume: number) => {
    if (position) {
      const minVolume = Math.max(0.01, newVolume);
      const maxVolume = position.volume;
      const clampedVolume = Math.min(maxVolume, minVolume);
      
      setVolume(parseFloat(clampedVolume.toFixed(2)));
      
      // Update percentage based on volume
      const newPercentage = (clampedVolume / position.volume) * 100;
      setPercentage(Math.round(newPercentage));
    }
  };

  const handlePercentageChange = (newPercentage: number) => {
    setPercentage(newPercentage);
  };

  const handleConfirm = () => {
    const finalVolume = Math.max(0.01, volume);
    onConfirm(finalVolume);
  };

  // Reset state when dialog opens
  useEffect(() => {
    if (isOpen && position) {
      setPercentage(100);
      setVolume(position.volume);
    }
  }, [isOpen, position]);

  if (!isOpen || !position) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-lg shadow-2xl max-w-md w-full border border-red-500/30">
        {/* Header */}
        <div className="p-6 border-b border-gray-700">
          <div className="flex items-center">
            <span className="text-2xl mr-3">⚠️</span>
            <h3 className="text-lg font-semibold text-white">Partial Close Position</h3>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="mb-4">
            <p className="text-gray-300 mb-2">
              Position #{position.ticket} - {position.symbol} ({position.type})
            </p>
            {/* <p className="text-gray-400 text-sm">
              Total Volume: {position.volume.toFixed(2)}
            </p> */}
          </div>

          {/* Percentage Slider */}
          <div className="mb-6">
            {/* <label className="block text-sm font-medium text-gray-300 mb-2">
              Close Percentage: {percentage}%
            </label> */}
            <input
              type="range"
              min="0"
              step="5"
              max="100"
              value={percentage}
              onChange={(e) => handlePercentageChange(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-400 mt-1">
              <span>1%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>

          {/* Volume Input */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Close Volume ({percentage}%)
            </label>
            <input
              type="number"
              min="0.01"
              max={position.volume}
              step="0.01"
              value={volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value) || 0.01)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
            <p className="text-xs text-gray-400 mt-1">
              Minimum: 0.01, Maximum: {position.volume.toFixed(2)}
            </p>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3 p-6 pt-0">
          <button
            onClick={onCancel}
            className="flex-1 py-2 px-4 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleConfirm}
            className="flex-1 py-2 px-4 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          >
            Close {volume.toFixed(2)} Lots
          </button>
        </div>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #ef4444;
          cursor: pointer;
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #ef4444;
          cursor: pointer;
          border: none;
        }
      `}</style>
    </div>
  );
};

export default PartialCloseDialog;
