// Test script to verify Reason field parsing functionality
// This can be run in the browser console to test the parsing

const testLongText = `Signal ID: a7b9c2d4
C.GPT
Symbol: XAUUSD
Signal: Buy Limit
Price: 2045.50
SL: 2040.00
TP1: 2050.00
TP2: 2055.00
TP3: 2060.00
TP4: 2065.00
TP5: 2070.00
Reason: RSI oversold at 25, MACD bullish crossover, price bounced off key support at 2040. EMA20 trending up, confluence of Fibonacci 61.8% retracement and pivot point support suggests strong buying opportunity with favorable risk-reward ratio.
Risk: Market volatility during US session. Watch for news events that could reverse trend. SL placement below key support minimizes downside. Position size should account for potential gap risk during market open.`;

console.log('Testing Reason and Risk field parsing...');
console.log('Sample text:', testLongText);

// Test the parsing logic
const lines = testLongText.split("\n").map((line) => line.trim());
let parsedReason = "";
let parsedRisk = "";

lines.forEach((line) => {
  const reasonMatch = line.match(/Reason\s*:\s*(.+)/i);
  if (reasonMatch) {
    parsedReason = reasonMatch[1].trim();
    console.log('Found Reason:', parsedReason);
  }

  const riskMatch = line.match(/Risk\s*:\s*(.+)/i);
  if (riskMatch) {
    parsedRisk = riskMatch[1].trim();
    console.log('Found Risk:', parsedRisk);
  }
});

if (parsedReason) {
  console.log('✅ Reason parsing successful!');
  console.log('Parsed Reason:', parsedReason);
} else {
  console.log('❌ Reason parsing failed!');
}

if (parsedRisk) {
  console.log('✅ Risk parsing successful!');
  console.log('Parsed Risk:', parsedRisk);
} else {
  console.log('❌ Risk parsing failed!');
}

// Test with different formats
const testCases = [
  'Reason: Simple reason text',
  'reason: lowercase reason',
  'REASON: uppercase reason',
  'Reason:No space after colon',
  'Reason:   Multiple spaces after colon',
  'Risk: Simple risk text',
  'risk: lowercase risk',
  'RISK: uppercase risk',
  'Risk:No space after colon',
  'Risk:   Multiple spaces after colon',
];

console.log('\nTesting different Reason and Risk formats:');
testCases.forEach((testCase, index) => {
  const reasonMatch = testCase.match(/Reason\s*:\s*(.+)/i);
  const riskMatch = testCase.match(/Risk\s*:\s*(.+)/i);

  if (reasonMatch) {
    console.log(`✅ Reason Test ${index + 1}: "${testCase}" -> "${reasonMatch[1].trim()}"`);
  } else if (riskMatch) {
    console.log(`✅ Risk Test ${index + 1}: "${testCase}" -> "${riskMatch[1].trim()}"`);
  } else {
    console.log(`❌ Test ${index + 1}: "${testCase}" -> No match`);
  }
});
