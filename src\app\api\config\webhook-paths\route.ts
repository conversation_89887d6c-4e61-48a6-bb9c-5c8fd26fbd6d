// app/api/config/webhook-paths/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { clearWebhookConfigCache } from '@/utils/webhookConfig';

export interface WebhookPathConfig {
  id: string;
  path: string;
  name: string;
  description: string;
  webhookHost: string;
  webhookAccessToken: string;
  enabled: boolean;
  createdAt: string;
  updatedAt?: string;
}

const CONFIG_DIR = path.join(process.cwd(), 'config');
const WEBHOOK_PATHS_CONFIG_FILE = path.join(CONFIG_DIR, 'webhook-paths.json');

// Ensure config directory exists
async function ensureConfigDir() {
  try {
    await fs.access(CONFIG_DIR);
  } catch {
    await fs.mkdir(CONFIG_DIR, { recursive: true });
  }
}

async function loadWebhookPaths(): Promise<WebhookPathConfig[]> {
  try {
    await ensureConfigDir();
    const data = await fs.readFile(WEBHOOK_PATHS_CONFIG_FILE, 'utf8');
    return JSON.parse(data);
  } catch {
    // Return default configuration if file doesn't exist
    const defaultConfig: WebhookPathConfig[] = [
      {
        id: "default",
        path: "/",
        name: "Default",
        description: "Default webhook configuration",
        webhookHost: process.env.WEBHOOK_HOST || "http://localhost:5050",
        webhookAccessToken: process.env.WEBHOOK_ACCESS_TOKEN || "",
        enabled: true,
        createdAt: new Date().toISOString()
      }
    ];
    await saveWebhookPaths(defaultConfig);
    return defaultConfig;
  }
}

async function saveWebhookPaths(configs: WebhookPathConfig[]): Promise<void> {
  await ensureConfigDir();
  await fs.writeFile(WEBHOOK_PATHS_CONFIG_FILE, JSON.stringify(configs, null, 2));
  // Clear cache so changes take effect immediately
  clearWebhookConfigCache();
}

export async function GET() {
  try {
    const configs = await loadWebhookPaths();
    return NextResponse.json(configs);
  } catch (error) {
    console.error('Failed to load webhook path configs:', error);
    return NextResponse.json({ error: 'Failed to load webhook path configs' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const newConfig: Omit<WebhookPathConfig, 'id' | 'createdAt'> = await request.json();
    const configs = await loadWebhookPaths();
    
    const config: WebhookPathConfig = {
      ...newConfig,
      id: `path_${Date.now()}`,
      createdAt: new Date().toISOString(),
    };
    
    configs.push(config);
    await saveWebhookPaths(configs);
    
    return NextResponse.json(config, { status: 201 });
  } catch (error) {
    console.error('Failed to create webhook path config:', error);
    return NextResponse.json({ error: 'Failed to create webhook path config' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const updatedConfig: WebhookPathConfig = await request.json();
    const configs = await loadWebhookPaths();
    
    const index = configs.findIndex(config => config.id === updatedConfig.id);
    if (index === -1) {
      return NextResponse.json({ error: 'Webhook path config not found' }, { status: 404 });
    }
    
    configs[index] = {
      ...updatedConfig,
      updatedAt: new Date().toISOString(),
    };
    
    await saveWebhookPaths(configs);
    return NextResponse.json(configs[index]);
  } catch (error) {
    console.error('Failed to update webhook path config:', error);
    return NextResponse.json({ error: 'Failed to update webhook path config' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'ID parameter is required' }, { status: 400 });
    }
    
    const configs = await loadWebhookPaths();
    const filteredConfigs = configs.filter(config => config.id !== id);
    
    if (filteredConfigs.length === configs.length) {
      return NextResponse.json({ error: 'Webhook path config not found' }, { status: 404 });
    }
    
    await saveWebhookPaths(filteredConfigs);
    return NextResponse.json({ message: 'Webhook path config deleted successfully' });
  } catch (error) {
    console.error('Failed to delete webhook path config:', error);
    return NextResponse.json({ error: 'Failed to delete webhook path config' }, { status: 500 });
  }
}
