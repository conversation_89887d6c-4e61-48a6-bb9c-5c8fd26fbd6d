'use client';

import React, { useState, useEffect } from 'react';
import { WebhookPathConfig } from '@/app/api/config/webhook-paths/route';

export default function WebhookPathManager() {
  const [configs, setConfigs] = useState<WebhookPathConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingConfig, setEditingConfig] = useState<WebhookPathConfig | null>(null);
  const [showForm, setShowForm] = useState(false);

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      const response = await fetch('/api/config/webhook-paths');
      const data = await response.json();
      setConfigs(data);
    } catch (error) {
      console.error('Failed to load webhook path configs:', error);
    } finally {
      setLoading(false);
    }
  };

  const saveConfig = async (config: Partial<WebhookPathConfig>) => {
    try {
      const method = editingConfig ? 'PUT' : 'POST';
      const response = await fetch('/api/config/webhook-paths', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config),
      });

      if (response.ok) {
        await loadConfigs();
        setEditingConfig(null);
        setShowForm(false);
      }
    } catch (error) {
      console.error('Failed to save webhook path config:', error);
    }
  };

  const deleteConfig = async (id: string) => {
    if (!confirm('Are you sure you want to delete this configuration?')) return;

    try {
      const response = await fetch(`/api/config/webhook-paths?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await loadConfigs();
      }
    } catch (error) {
      console.error('Failed to delete webhook path config:', error);
    }
  };

  if (loading) {
    return <div className="p-4">Loading webhook path configurations...</div>;
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Webhook Path Configuration</h2>
        <button
          onClick={() => {
            setEditingConfig(null);
            setShowForm(true);
          }}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          Add New Path
        </button>
      </div>

      <div className="grid gap-4">
        {configs.map((config) => (
          <div key={config.id} className="border rounded-lg p-4 bg-white shadow">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="font-semibold text-lg">{config.name}</h3>
                <p className="text-gray-600 mb-2">{config.description}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Path:</span> {config.path}
                  </div>
                  <div>
                    <span className="font-medium">Host:</span> {config.webhookHost}
                  </div>
                  <div>
                    <span className="font-medium">Status:</span>{' '}
                    <span className={config.enabled ? 'text-green-600' : 'text-red-600'}>
                      {config.enabled ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Token:</span>{' '}
                    {config.webhookAccessToken ? '••••••••' : 'Not set'}
                  </div>
                </div>
              </div>
              <div className="flex gap-2 ml-4">
                <button
                  onClick={() => {
                    setEditingConfig(config);
                    setShowForm(true);
                  }}
                  className="bg-yellow-500 text-white px-3 py-1 rounded text-sm hover:bg-yellow-600"
                >
                  Edit
                </button>
                <button
                  onClick={() => deleteConfig(config.id)}
                  className="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {showForm && (
        <WebhookPathForm
          config={editingConfig}
          onSave={saveConfig}
          onCancel={() => {
            setShowForm(false);
            setEditingConfig(null);
          }}
        />
      )}
    </div>
  );
}

interface WebhookPathFormProps {
  config: WebhookPathConfig | null;
  onSave: (config: Partial<WebhookPathConfig>) => void;
  onCancel: () => void;
}

function WebhookPathForm({ config, onSave, onCancel }: WebhookPathFormProps) {
  const [formData, setFormData] = useState({
    path: config?.path || '',
    name: config?.name || '',
    description: config?.description || '',
    webhookHost: config?.webhookHost || '',
    webhookAccessToken: config?.webhookAccessToken || '',
    enabled: config?.enabled ?? true,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(config ? { ...config, ...formData } : formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 className="text-lg font-semibold mb-4">
          {config ? 'Edit' : 'Add'} Webhook Path Configuration
        </h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Path</label>
            <input
              type="text"
              value={formData.path}
              onChange={(e) => setFormData({ ...formData, path: e.target.value })}
              className="w-full border rounded px-3 py-2"
              placeholder="/bb"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full border rounded px-3 py-2"
              placeholder="BB Path"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Description</label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full border rounded px-3 py-2"
              placeholder="Webhook configuration for /bb path"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Webhook Host</label>
            <input
              type="url"
              value={formData.webhookHost}
              onChange={(e) => setFormData({ ...formData, webhookHost: e.target.value })}
              className="w-full border rounded px-3 py-2"
              placeholder="http://localhost:5051"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Access Token</label>
            <input
              type="text"
              value={formData.webhookAccessToken}
              onChange={(e) => setFormData({ ...formData, webhookAccessToken: e.target.value })}
              className="w-full border rounded px-3 py-2"
              placeholder="Access token"
            />
          </div>
          <div className="flex items-center">
            <input
              type="checkbox"
              id="enabled"
              checked={formData.enabled}
              onChange={(e) => setFormData({ ...formData, enabled: e.target.checked })}
              className="mr-2"
            />
            <label htmlFor="enabled" className="text-sm font-medium">
              Enabled
            </label>
          </div>
          <div className="flex gap-2 pt-4">
            <button
              type="submit"
              className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
            >
              Save
            </button>
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
