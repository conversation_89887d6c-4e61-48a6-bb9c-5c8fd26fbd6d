# CORS Fix Guide for Python Webhook

The error you're seeing is a CORS (Cross-Origin Resource Sharing) issue. Your Python webhook needs to allow requests from your Next.js app running on localhost:3002.

## For Flask Applications

Add this to your Python Flask webhook:

```python
from flask import Flask, request, jsonify
from flask_cors import CORS

app = Flask(__name__)

# Enable CORS for all routes and origins
CORS(app, origins=["http://localhost:3002", "http://localhost:3000"])

# Or for development, allow all origins (less secure)
# CORS(app, origins="*")

@app.route('/webhook_ai_analysis', methods=['POST', 'OPTIONS'])
def webhook_ai_analysis():
    # Handle preflight OPTIONS request
    if request.method == 'OPTIONS':
        response = jsonify({'status': 'ok'})
        response.headers.add('Access-Control-Allow-Origin', 'http://localhost:3002')
        response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Accept')
        response.headers.add('Access-Control-Allow-Methods', 'POST,OPTIONS')
        return response
    
    # Your existing POST logic here
    data = request.get_json()
    
    # Process the request...
    symbol = data.get("symbol")
    timeframes = data.get("timeframes", [])
    barback = data.get("barback", 100)
    custom_prompt = data.get("prompt", "")
    ai_provider = data.get("ai", "gpt").lower()
    image_url = data.get("image", "")
    use_signal_format = data.get("use_signal_format", True)
    
    # Your AI analysis logic here...
    
    response_data = {
        "error": False,
        "message": "AI analysis completed successfully",
        "symbol": symbol,
        "timeframes": timeframes,
        "ai_provider": ai_provider.upper(),
        "analysis": "Your analysis result here...",
        "custom_prompt": custom_prompt,
        "image_analyzed": bool(image_url),
        "use_signal_format": 1 if use_signal_format else 0,
        "timestamp": "2025-08-23T11:57:28.690262",
        "structured_signal": True
    }
    
    return jsonify(response_data)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5050, debug=True)
```

## For FastAPI Applications

Add this to your Python FastAPI webhook:

```python
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3002", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

@app.post("/webhook_ai_analysis")
async def webhook_ai_analysis(request: Request):
    data = await request.json()
    
    # Process the request...
    symbol = data.get("symbol")
    timeframes = data.get("timeframes", [])
    barback = data.get("barback", 100)
    custom_prompt = data.get("prompt", "")
    ai_provider = data.get("ai", "gpt").lower()
    image_url = data.get("image", "")
    use_signal_format = data.get("use_signal_format", True)
    
    # Your AI analysis logic here...
    
    response_data = {
        "error": False,
        "message": "AI analysis completed successfully",
        "symbol": symbol,
        "timeframes": timeframes,
        "ai_provider": ai_provider.upper(),
        "analysis": "Your analysis result here...",
        "custom_prompt": custom_prompt,
        "image_analyzed": bool(image_url),
        "use_signal_format": 1 if use_signal_format else 0,
        "timestamp": "2025-08-23T11:57:28.690262",
        "structured_signal": True
    }
    
    return JSONResponse(content=response_data)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=5050)
```

## Install Required Packages

For Flask:
```bash
pip install flask flask-cors
```

For FastAPI:
```bash
pip install fastapi uvicorn
```

## Environment Variable

Make sure your `.env` file has:
```env
NEXT_PUBLIC_WEBHOOK_URL_AI_ANALYSIS=http://localhost:5050/webhook_ai_analysis
```

## Testing Steps

1. Update your Python webhook with CORS configuration
2. Restart your Python server
3. Use the "Test Connection" button in the AI Bot tab
4. If successful, try running an AI analysis

## Common Issues

- **Port conflicts**: Make sure port 5050 is available
- **Firewall**: Ensure localhost connections are allowed
- **URL mismatch**: Verify the webhook URL matches exactly
- **Headers**: Some servers require specific headers

The updated AI Bot now includes better error messages and a connection test button to help debug these issues.
