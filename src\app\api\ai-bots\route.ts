// app/api/ai-bots/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { getConfigFilePathForPath, getConfigDirForPath, getPathFromHeaders } from '@/utils/pathConfig';

interface AIBotConfig {
  id: string;
  name: string;
  enabled: boolean;
  aiProvider: "gpt" | "gemini";
  webhookUrl: string;
  autoPlaceOrder: boolean;
  scheduleCheck: boolean;
  checkInterval: number;
  createdAt: string;
  lastActive?: string;
}

const CONFIG_DIR = path.join(process.cwd(), 'config');
const LEGACY_AI_BOTS_CONFIG_FILE = path.join(process.cwd(), 'ai-bots.json');

// Ensure config directory exists for a specific path
async function ensureConfigDir(configDir: string) {
  try {
    await fs.access(configDir);
  } catch {
    await fs.mkdir(configDir, { recursive: true });
  }
}

async function loadBots(refererPath: string): Promise<AIBotConfig[]> {
  try {
    const configDir = getConfigDirForPath(refererPath);
    const aiBotsFile = getConfigFilePathForPath(refererPath, 'ai-bots.json');

    await ensureConfigDir(configDir);

    // Try to load from path-specific config folder
    try {
      const data = await fs.readFile(aiBotsFile, 'utf8');
      console.log(`Loaded AI bots from: ${aiBotsFile}`);
      return JSON.parse(data);
    } catch {
      // Return empty array for new path-specific configs
      console.log(`No AI bots config found at: ${aiBotsFile}, returning empty array`);
      return [];
    }
  } catch (error) {
    console.error(`Failed to load AI bots for path ${refererPath}:`, error);
    return [];
  }
}

async function saveBots(bots: AIBotConfig[], refererPath: string): Promise<void> {
  const configDir = getConfigDirForPath(refererPath);
  const aiBotsFile = getConfigFilePathForPath(refererPath, 'ai-bots.json');

  await ensureConfigDir(configDir);
  await fs.writeFile(aiBotsFile, JSON.stringify(bots, null, 2));
  console.log(`Saved AI bots to: ${aiBotsFile}`);
}

export async function GET(request: NextRequest) {
  try {
    const refererPath = getPathFromHeaders(request);
    console.log(`Loading AI bots for path: ${refererPath}`);

    const bots = await loadBots(refererPath);
    return NextResponse.json(bots);
  } catch (error) {
    console.error('Failed to load AI bots:', error);
    return NextResponse.json({ error: 'Failed to load AI bots' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const refererPath = getPathFromHeaders(request);
    console.log(`Saving AI bots for path: ${refererPath}`);

    const bots: AIBotConfig[] = await request.json();
    await saveBots(bots, refererPath);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save AI bots:', error);
    return NextResponse.json({ error: 'Failed to save AI bots' }, { status: 500 });
  }
}
