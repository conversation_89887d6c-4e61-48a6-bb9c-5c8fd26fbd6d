// app/api/current-price/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getWebhookConfigForPath, buildWebhookUrls } from '@/utils/webhookConfig';
import { getPathFromHeaders } from '@/utils/pathConfig';

interface CurrentPriceRequest {
  symbol: string;
}

interface CurrentPriceResponse {
  error: boolean;
  symbol: string;
  full_symbol: string;
  bid: number;
  ask: number;
  last: number;
  spread: number;
  spread_points: number;
  digits: number;
  point: number;
  time: number;
  timestamp: number;
  volume: number;
}

export async function POST(request: NextRequest) {
  try {
    const data: CurrentPriceRequest = await request.json();

    // Get the current path from headers
    const refererPath = getPathFromHeaders(request);
    const webhookConfig = await getWebhookConfigForPath(refererPath);

    // Build webhook URLs with the path-specific host
    const webhookUrls = buildWebhookUrls(webhookConfig.webhookHost);
    const webhookUrl = webhookUrls.current_price;

    if (!webhookUrl) {
      return NextResponse.json(
        { error: true, message: 'Current price webhook URL not configured' },
        { status: 500 }
      );
    }

    console.log(`Fetching current price from: ${webhookUrl}`);
    console.log('Symbol:', data.symbol);

    // Get access token from path-specific configuration
    const accessToken = webhookConfig.webhookAccessToken;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'NextJS-CurrentPrice-Proxy/1.0',
      'Accept': 'application/json',
    };

    // Add access token to headers if available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
      headers['X-Access-Token'] = accessToken;
    }

    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        symbol: data.symbol,
        // Add metadata
        _meta: {
          source: 'web_app',
          timestamp: new Date().toISOString(),
          accessToken: accessToken ? 'provided' : 'not_provided'
        }
      }),
    });

    const responseText = await response.text();
    console.log('Current price webhook response:', responseText);

    let responseData: CurrentPriceResponse;
    try {
      responseData = JSON.parse(responseText);
    } catch (parseError) {
      console.error('Failed to parse current price response:', parseError);
      return NextResponse.json(
        { error: true, message: 'Invalid response from current price webhook' },
        { status: 500 }
      );
    }

    // If the webhook server returned an error status, mark as unsuccessful
    const success = response.ok && !responseData.error;

    // Log error details if webhook failed
    if (!success) {
      console.error('Current Price Webhook Error Details:');
      console.error('- Status:', response.status);
      console.error('- Status Text:', response.statusText);
      console.error('- Response:', responseData);
    }

    return NextResponse.json({
      success,
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      webhookUrl, // Include for debugging
      error: !success ? {
        type: response.status === 500 ? 'webhook_internal_error' : 'webhook_error',
        message: `Current price webhook returned ${response.status}: ${response.statusText}`,
        details: responseData.error ? 'Webhook returned error' : 'Unknown error'
      } : undefined
    });

  } catch (error: unknown) {
    console.error('Current price proxy error:', error);
    return NextResponse.json(
      {
        error: true,
        message: (error as Error).message || 'Internal server error',
        details: String(error)
      },
      { status: 500 }
    );
  }
}
