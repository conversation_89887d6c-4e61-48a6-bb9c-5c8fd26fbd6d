// test-webhook-paths.js
// Simple test script to verify path-based webhook configuration

const testPaths = [
  { path: '/', expectedHost: 'http://localhost:5050' },
  { path: '/zd', expectedHost: 'http://localhost:5050' },
  { path: '/bb', expectedHost: 'http://localhost:5051' }
];

async function testWebhookConfig() {
  console.log('Testing webhook path configuration...\n');
  
  for (const test of testPaths) {
    try {
      // Test the webhook API with different referer paths
      const response = await fetch('http://localhost:3001/api/webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Referer': `http://localhost:3001${test.path}`,
          'x-referer-path': test.path
        },
        body: JSON.stringify({
          group: 'g_instant',
          data: { test: true, message: `Test from ${test.path}` }
        })
      });
      
      const result = await response.json();
      console.log(`Path: ${test.path}`);
      console.log(`Expected Host: ${test.expectedHost}`);
      console.log(`Response:`, result);
      console.log('---\n');
      
    } catch (error) {
      console.error(`Error testing path ${test.path}:`, error.message);
    }
  }
}

// Test configuration loading
async function testConfigAPI() {
  console.log('Testing webhook path configuration API...\n');
  
  try {
    const response = await fetch('http://localhost:3001/api/config/webhook-paths');
    const configs = await response.json();
    
    console.log('Loaded webhook path configurations:');
    configs.forEach(config => {
      console.log(`- ${config.path}: ${config.webhookHost} (${config.name})`);
    });
    console.log('\n');
    
  } catch (error) {
    console.error('Error loading webhook path configs:', error.message);
  }
}

// Run tests
async function runTests() {
  console.log('=== Webhook Path Configuration Tests ===\n');
  
  await testConfigAPI();
  await testWebhookConfig();
  
  console.log('Tests completed!');
}

// Check if running directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testWebhookConfig, testConfigAPI, runTests };
