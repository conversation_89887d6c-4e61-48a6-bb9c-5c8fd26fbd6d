// Test script for current price API
const http = require('http');

const testCurrentPrice = async () => {
  const postData = JSON.stringify({
    symbol: 'EURUSD'
  });

  const options = {
    hostname: 'localhost',
    port: 3002,
    path: '/api/current-price',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
      'x-current-path': '/zd' // Simulate the path header
    }
  };

  console.log('Testing current price API...');
  console.log('Request data:', { symbol: 'EURUSD' });

  const req = http.request(options, (res) => {
    console.log(`Status: ${res.statusCode}`);
    console.log(`Headers:`, res.headers);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('Response:', JSON.stringify(response, null, 2));
        
        if (response.success && response.data && !response.data.error) {
          console.log('\n✅ Current price fetched successfully!');
          console.log(`Symbol: ${response.data.symbol}`);
          console.log(`Bid: ${response.data.bid}`);
          console.log(`Ask: ${response.data.ask}`);
          console.log(`Spread: ${response.data.spread}`);
        } else {
          console.log('\n❌ Current price fetch failed');
          console.log('Error:', response.error || response.data?.message || 'Unknown error');
        }
      } catch (parseError) {
        console.log('\n❌ Failed to parse response');
        console.log('Raw response:', data);
        console.log('Parse error:', parseError.message);
      }
    });
  });

  req.on('error', (err) => {
    console.log('\n❌ Request failed');
    console.log('Error:', err.message);
  });

  req.write(postData);
  req.end();
};

// Run the test
testCurrentPrice();
