"use client";
import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import { useAppSettings } from "@/contexts/AppSettingsContext";
import TabInput from "../components/TabInput";
import TabInstant from "../components/TabInstant";

import TabControl from "../components/TabControl";
import TabCalendar from "../components/TabCalendar";
import TabDiscordBot from "../components/TabDiscordBot";
import TabAIBot from "../components/TabAIBot";
import TabOrders from "../components/TabOrders";
 


export default function Home() {
  const { settings } = useAppSettings();
  const pathname = usePathname();

  // Initialize activeTab from localStorage or default to g_input
  const [activeTab, setActiveTab] = useState<"g_instant" | "g_input" | "g_control" | "g_calendar" | "g_discord" | "g_aibot" | "g_orders">(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('activeTab');
      if (saved && ['g_instant', 'g_input', 'g_control', 'g_calendar', 'g_discord', 'g_aibot', 'g_orders'].includes(saved)) {
        return saved as "g_instant" | "g_input" | "g_control" | "g_calendar" | "g_discord" | "g_aibot" | "g_orders";
      }
    }
    return "g_orders";
  });

  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  // Save activeTab to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('activeTab', activeTab);
      console.log('Tab changed to:', activeTab);
    }
  }, [activeTab]);

  // Wrapper function for setActiveTab with debugging
  const changeTab = (newTab: "g_instant" | "g_input" | "g_control" | "g_calendar" | "g_discord" | "g_aibot" | "g_orders") => {
    console.log('changeTab called:', newTab, 'from:', activeTab);
    console.trace('Tab change stack trace');
    setActiveTab(newTab);
  };

  // Event listener for tab switching
  useEffect(() => {
    const handleSwitchToInputTab = () => {
      console.log('Switching to Input tab via event');
      changeTab("g_input");
    };

    window.addEventListener('switchToInputTab', handleSwitchToInputTab);

    return () => {
      window.removeEventListener('switchToInputTab', handleSwitchToInputTab);
    };
  }, []);
 
  const sendWebhook = async (
    group: "g_instant" | "g_input" | "g_control" | "g_discord",
    url: string,
    data: {}
  ) => {
    if (!url) {
      alert("❌ Webhook URL ไม่ถูกตั้งค่าใน .env.local");
      return;
    }

    setLoading(true);
    setLogs((prev) => [`⏳ Sending to ${group} with: ${JSON.stringify(data)}`, ...prev]);

    try {
      // Use the proxy API route instead of direct webhook call
      const res = await fetch('/api/webhook', {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "x-current-path": pathname
        },
        body: JSON.stringify({ group, data }),
      });

      // Log response status for debugging
      setLogs((prev) => [`📊 Proxy Response Status: ${res.status} ${res.statusText}`, ...prev]);

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status} ${res.statusText}`);
      }

      const result = await res.json();

      if (result.success) {
        setLogs((prev) => [`✅ Success: ${JSON.stringify(result.data)}`, ...prev]);
      } else {
        setLogs((prev) => [`❌ Webhook Error (${result.status}): ${JSON.stringify(result.data)}`, ...prev]);
      }

    } catch (err: any) {
      // More detailed error logging
      console.error('Webhook error:', err);

      if (err.name === 'TypeError' && err.message.includes('fetch')) {
        setLogs((prev) => [`❌ Network Error: Cannot reach proxy API. Check if your Next.js server is running.`, ...prev]);
      } else if (err.message.includes('HTTP error')) {
        setLogs((prev) => [`❌ HTTP Error: ${err.message}`, ...prev]);
      } else {
        setLogs((prev) => [`❌ Error: ${err.message}`, ...prev]);
      }
    } finally {
      setLoading(false);
    }
  };

  const customStyle = {
    option: (base: any, state: any) => ({
      ...base,
      backgroundColor: state.isSelected
        ? "#2563eb" // สีเมื่อเลือก
        : state.isFocused
          ? "#1e293b" // สีเมื่อ hover
          : "#0f172a", // สีปกติ
      color: "white",
      cursor: "pointer",
    }),
    control: (base: any) => ({
      ...base,
      backgroundColor: "#1f2937",
      color: "white",
      borderColor: "#4b5563",
    }),
    menu: (base: any) => ({
      ...base,
      backgroundColor: "#1f2937",
      color: "white",
    }),
    singleValue: (base: any) => ({ ...base, color: "white" }),
    input: (base: any) => ({ ...base, color: "white" }),
    placeholder: (base: any) => ({ ...base, color: "#d1d5db" }),
  }; 

  return (
    <div className="min-h-screen w-full overflow-x-hidden font-[family-name:var(--font-geist-sans)]">
      <main className="w-full max-w-full">
        <div className="w-full max-w-7xl mx-auto p-4 text-white bg-gray-900 min-h-screen">
          {settings.showTabNavigation && (
            <div className="flex mb-6 border-b border-gray-700">
              {settings.visibleTabs.orders && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_orders" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => changeTab("g_orders")}
                >
                  Orders
                </button>
              )}
              {settings.visibleTabs.aibot && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_aibot" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => changeTab("g_aibot")}
                >
                  AI
                </button>
              )}
              {settings.visibleTabs.input && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_input" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => changeTab("g_input")}
                >
                  Input
                </button>
              )}
              {settings.visibleTabs.instant && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_instant" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => changeTab("g_instant")}
                >
                  Instant
                </button>
              )}
              {settings.visibleTabs.control && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_control" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => changeTab("g_control")}
                >
                  Control
                </button>
              )}
              {settings.visibleTabs.calendar && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_calendar" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => changeTab("g_calendar")}
                >
                  Calendar
                </button>
              )}
              {settings.visibleTabs.discord && (
                <button
                  className={`px-4 py-2 text-sm font-medium ${activeTab === "g_discord" ? "border-b-2 border-indigo-400" : "text-gray-400"
                    }`}
                  onClick={() => changeTab("g_discord")}
                >
                  Discord Bot
                </button>
              )}
            </div>
          )}

          {/* Connection Test Buttons */}
          {/* <div className="flex space-x-2 mb-4">
            <button
              onClick={() => testConnection(process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP1 || '', 'g_instant')}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              disabled={loading}
            >
              🔍 Test Group 1
            </button>
            <button
              onClick={() => testConnection(process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP2 || '', 'g_input')}
              className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
              disabled={loading}
            >
              🔍 Test Group 2
            </button>
          </div> */}

          <div className={`grid gap-4 w-full max-w-full ${activeTab === "g_calendar" || activeTab === "g_discord" || activeTab === "g_aibot" || activeTab === "g_orders" ? "grid-cols-1" : "grid-cols-1 lg:grid-cols-2"}`}>
            <div className="w-full max-w-full overflow-hidden">
              {activeTab === "g_instant" ? (
                <TabInstant customStyle={customStyle} sendWebhook={sendWebhook} loading={loading} />
              ) :  activeTab === "g_input" ? (
                <TabInput customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : activeTab === "g_control" ? (
                <TabControl customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : activeTab === "g_discord" ? (
                <TabDiscordBot customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : activeTab === "g_aibot" ? (
                <TabAIBot customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : activeTab === "g_orders" ? (
                <TabOrders customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              ) : (
                <TabCalendar customStyle={customStyle}  sendWebhook={sendWebhook} loading={loading} />
              )}
            </div>

            {/* Hide Response Logs column when calendar, discord, aibot or orders tab is active */}
            {activeTab !== "g_calendar" && activeTab !== "g_discord" && activeTab !== "g_aibot" && activeTab !== "g_orders" && (
              <div className="w-full max-w-full overflow-hidden">
                <h2 className="text-lg font-semibold mb-2">📜 Response Logs</h2>
                <div className="bg-gray-800 border border-gray-700 rounded-lg p-4 h-80 overflow-y-auto text-sm w-full">
                  {logs.length === 0 ? (
                    <p className="text-gray-500">ไม่มี log</p>
                  ) : (
                    logs.map((log, idx) => (
                      <div key={idx} className="mb-2 text-gray-300">
                        {log}
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
