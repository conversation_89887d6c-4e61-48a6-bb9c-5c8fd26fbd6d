# Path-Based Webhook Configuration

This system allows you to configure different webhook hosts and access tokens for different URL paths in your application.

## Overview

- `/zd` path → uses `http://localhost:5050` with its own access token
- `/bb` path → uses `http://localhost:5051` with its own access token  
- Default path → fallback configuration for any other paths

## Configuration Files

### 1. Environment Variables (.env and .env.production)

```bash
# Default/Fallback webhook settings
WEBHOOK_HOST=http://localhost:5050
WEBHOOK_ACCESS_TOKEN=your_default_token_here

# Path-specific settings
WEBHOOK_HOST_ZD=http://localhost:5050
WEBHOOK_ACCESS_TOKEN_ZD=your_zd_token_here

WEBHOOK_HOST_BB=http://localhost:5051
WEBHOOK_ACCESS_TOKEN_BB=your_bb_token_here
```

### 2. Configuration File (config/webhook-paths.json)

```json
[
  {
    "id": "zd",
    "path": "/zd",
    "name": "ZD Path",
    "description": "Webhook configuration for /zd path",
    "webhookHost": "http://localhost:5050",
    "webhookAccessToken": "your_zd_token_here",
    "enabled": true,
    "createdAt": "2025-11-27T00:00:00.000Z"
  },
  {
    "id": "bb", 
    "path": "/bb",
    "name": "BB Path",
    "description": "Webhook configuration for /bb path",
    "webhookHost": "http://localhost:5051",
    "webhookAccessToken": "your_bb_token_here",
    "enabled": true,
    "createdAt": "2025-11-27T00:00:00.000Z"
  }
]
```

## How It Works

1. **Middleware Detection**: The Next.js middleware (`middleware.ts`) detects the current URL path and adds it to request headers.

2. **Path Resolution**: API routes use the `getWebhookConfigForPath()` utility function to get the appropriate webhook configuration based on the current path.

3. **Dynamic URLs**: Webhook URLs are built dynamically using the path-specific host:
   - `/zd` → `http://localhost:5050/webhook_instant`
   - `/bb` → `http://localhost:5051/webhook_instant`

4. **Access Tokens**: Each path uses its own access token for authentication.

## API Routes Updated

The following API routes now support path-based webhook configuration:

- `/api/webhook` - Main webhook proxy
- `/api/proxy` - General proxy
- `/api/fast-proxy` - Fast proxy
- `/api/orders/action` - Order actions
- `/api/orders/data` - Order data

## Management Interface

Visit `/admin/webhook-paths` to manage webhook path configurations through a web interface.

## API Endpoints

### GET /api/config/webhook-paths
Get all webhook path configurations.

### POST /api/config/webhook-paths
Create a new webhook path configuration.

### PUT /api/config/webhook-paths
Update an existing webhook path configuration.

### DELETE /api/config/webhook-paths?id={id}
Delete a webhook path configuration.

## Testing

Run the test script to verify the configuration:

```bash
node test-webhook-paths.js
```

This will test that different paths use their respective webhook hosts and access tokens.

## URL Structure

- `http://localhost:3001/` - Default path (uses default webhook config)
- `http://localhost:3001/zd` - ZD path (uses localhost:5050)
- `http://localhost:3001/bb` - BB path (uses localhost:5051)

## Adding New Paths

1. Add environment variables for the new path:
   ```bash
   WEBHOOK_HOST_NEWPATH=http://localhost:5052
   WEBHOOK_ACCESS_TOKEN_NEWPATH=new_path_token
   ```

2. Create the page route: `src/app/newpath/page.tsx`

3. Add configuration via the admin interface or directly in `config/webhook-paths.json`

4. The system will automatically use the new configuration for API calls from that path.

## Backward Compatibility

The system maintains backward compatibility with existing environment variables:
- `WEBHOOK_HOST` - Used as fallback
- `WEBHOOK_ACCESS_TOKEN` - Used as fallback
- All existing `NEXT_PUBLIC_WEBHOOK_URL_*` variables still work but are now dynamically generated

## Security Notes

- Access tokens are stored in the configuration file - ensure proper file permissions
- Consider using environment variables for sensitive tokens in production
- The admin interface should be protected in production environments
