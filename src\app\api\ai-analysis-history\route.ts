// app/api/ai-analysis-history/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import { getConfigFilePathForPath, getConfigDirForPath, getPathFromHeaders } from '@/utils/pathConfig';

interface AnalysisHistoryItem {
  id: string;
  botName: string;
  timestamp: string;
  response: {
    error: boolean;
    message: string;
    symbol: string;
    timeframes: string[];
    ai_provider: string;
    analysis: string;
    custom_prompt: string;
    image_analyzed: boolean;
    use_signal_format: number;
    timestamp: string;
    signal_data?: {
      signal_id: string;
      symbol: string;
      signal_type: string;
      entry_price: string;
      sl_price: string;
      tp1_price: string;
      tp2_price: string;
      tp3_price: string;
      tp4_price?: string;
      tp5_price?: string;
    };
    structured_signal: boolean;
  };
}

const CONFIG_DIR = path.join(process.cwd(), 'config');
const LEGACY_ANALYSIS_HISTORY_FILE = path.join(process.cwd(), 'ai-analysis-history.json');

// Ensure config directory exists for a specific path
async function ensureConfigDir(configDir: string) {
  try {
    await fs.access(configDir);
  } catch {
    await fs.mkdir(configDir, { recursive: true });
  }
}

async function loadHistory(refererPath: string): Promise<AnalysisHistoryItem[]> {
  try {
    const configDir = getConfigDirForPath(refererPath);
    const historyFile = getConfigFilePathForPath(refererPath, 'ai-analysis-history.json');

    await ensureConfigDir(configDir);

    // Try to load from path-specific config folder
    try {
      const data = await fs.readFile(historyFile, 'utf8');
      console.log(`Loaded AI analysis history from: ${historyFile}`);
      return JSON.parse(data);
    } catch {
      // Return empty array for new path-specific configs
      console.log(`No AI analysis history found at: ${historyFile}, returning empty array`);
      return [];
    }
  } catch (error) {
    console.error(`Failed to load AI analysis history for path ${refererPath}:`, error);
    return [];
  }
}

async function saveHistory(history: AnalysisHistoryItem[], refererPath: string): Promise<void> {
  const configDir = getConfigDirForPath(refererPath);
  const historyFile = getConfigFilePathForPath(refererPath, 'ai-analysis-history.json');

  await ensureConfigDir(configDir);
  // Keep only the last 1000 entries to prevent file from growing too large
  const limitedHistory = history.slice(0, 1000);
  await fs.writeFile(historyFile, JSON.stringify(limitedHistory, null, 2));
  console.log(`Saved AI analysis history to: ${historyFile}`);
}

export async function GET(request: NextRequest) {
  try {
    const refererPath = getPathFromHeaders(request);
    console.log(`Loading AI analysis history for path: ${refererPath}`);

    const history = await loadHistory(refererPath);
    return NextResponse.json(history);
  } catch (error) {
    console.error('Failed to load analysis history:', error);
    return NextResponse.json({ error: 'Failed to load analysis history' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const refererPath = getPathFromHeaders(request);
    console.log(`Saving AI analysis history for path: ${refererPath}`);

    const history: AnalysisHistoryItem[] = await request.json();
    await saveHistory(history, refererPath);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Failed to save analysis history:', error);
    return NextResponse.json({ error: 'Failed to save analysis history' }, { status: 500 });
  }
}
