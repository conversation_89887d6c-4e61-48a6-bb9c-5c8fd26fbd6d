// components/TabControl.tsx
"use client";
import { useState } from "react";
import Select from "react-select";
import { SYMBOL_OPTIONS } from "@/config/global";
import { useConfirmation } from "@/contexts/ConfirmationContext";
import ConfirmationDialog from "./ConfirmationDialog";

const WEBHOOK_GROUP_3 = process.env.NEXT_PUBLIC_WEBHOOK_URL_GROUP3;

export default function TabControl({
  customStyle,
  sendWebhook,
  loading,
}: {
  customStyle: Object,
  sendWebhook: Function;
  loading: boolean;
}) {
  const [data, setData] = useState({
    s: "XAUUSD",
    // s: "GBPUSD",
    a: "", // close  sl2be  tp2be
    c: "all", // (all  all-buy  all-sell)  all-profit  buy-profit sell-profit  all-loss  buy-loss  sell-loss
  });

  // Get confirmation setting from context
  const { requireConfirmation } = useConfirmation();

  // Confirmation dialog state
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [pendingAction, setPendingAction] = useState<string>("");

  const handleSubmitClick = (s: string, a: string, c: string) => {
    setData({ s:s, a:a, c:c });
    if (requireConfirmation) {
      // setPendingAction(s,a,c);
      setShowConfirmation(true);
    } else {
      // Send immediately without confirmation
      handleDirectSubmit();
    }
  };

  const handleDirectSubmit = () => {
    // Group 3 webhook
    sendWebhook("g_control", WEBHOOK_GROUP_3, {
      // s,a,c     
      ...data
    });
  };

  const handleConfirmSubmit = () => {
    handleDirectSubmit();
    setShowConfirmation(false);
    // setData({ s: "", a: "", c: "" }); // clear
    // setPendingAction("");
  };

  const handleCancelSubmit = () => {
    setShowConfirmation(false);
    // setData({ s: "", a: "", c: "" }); // clear
    // setPendingAction("");
  };

  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">

      <div className="flex flex-col sm:flex-row grid grid-cols-2 lg:grid-cols-2 gap-4 items-center">

        {/* Symbol */}
        <div className="w-full">
          <h3>
            Close Orders
          </h3>
        </div>
        <div className="w-full">
          <Select
            className="text-black float-right"
            // className="mt-1 w-full rounded-md bg-gray-800 text-white border border-gray-600 p-2"
            // value={data.symbol}
            value={SYMBOL_OPTIONS.find((opt) => opt.value === data.s)}
            // value={{label: data.symbol, value: data.symbol }}
            onChange={(e) => setData({ ...data, s: e?.value || "" })}
            options={SYMBOL_OPTIONS}
            isSearchable
            styles={customStyle}
          />
        </div>
      </div>


      {/* Close all*/}
      <div className="flex flex-col sm:flex-row grid grid-cols-3 lg:grid-cols-3 gap-4 items-center">
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "all")}
          className="text-xs w-full py-2 px-4 bg-cyan-600 text-white font-semibold rounded-md hover:bg-cyan-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE ALL"}
        </button>
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "all-buy")}
          className="text-xs w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE BUY"}
        </button>

        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "all-sell")}
          className="text-xs w-full py-2 px-4 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE SELL"}
        </button>
      </div>

      {/* Close profit*/}
      <div className="flex flex-col sm:flex-row grid grid-cols-3 lg:grid-cols-3 gap-4 items-center">
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "all-profit")}
          className="text-xs w-full py-2 px-4 bg-cyan-600 text-white font-semibold rounded-md hover:bg-cyan-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE ALL PROFIT"}
        </button>
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "buy-profit")}
          className="text-xs w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE BUY PROFIT"}
        </button>

        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "sell-profit")}
          className="text-xs w-full py-2 px-4 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE SELL PROFIT"}
        </button>
      </div>

      {/* Close loss*/}
      <div className="flex flex-col sm:flex-row grid grid-cols-3 lg:grid-cols-3 gap-4 items-center">
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "all-loss")}
          className="text-xs w-full py-2 px-4 bg-cyan-600 text-white font-semibold rounded-md hover:bg-cyan-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE ALL LOSS"}
        </button>
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "buy-loss")}
          className="text-xs w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE BUY LOSS"}
        </button>

        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "close", "sell-loss")}
          className="text-xs w-full py-2 px-4 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "CLOSE SELL LOSS"}
        </button>
      </div>

      <div className="w-full pt-3">
        <h3>
          Set STOPLOSS to BREAKEVEN
        </h3>
      </div>

      {/* Close all*/}
      <div className="flex flex-col sm:flex-row grid grid-cols-3 lg:grid-cols-3 gap-4 items-center">
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "sl2be", "all")}
          className="text-xs w-full py-2 px-4 bg-cyan-600 text-white font-semibold rounded-md hover:bg-cyan-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "ALL SL > BE"}
        </button>
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "sl2be", "all-buy")}
          className="text-xs w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "BUY SL > BE"}
        </button>

        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "sl2be", "all-sell")}
          className="text-xs w-full py-2 px-4 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "SELL SL > BE"}
        </button>
      </div>

      <div className="w-full pt-3">
        <h3>
          Set PROFIT to BREAKEVEN
        </h3>
      </div>

      {/* Close all*/}
      <div className="flex flex-col sm:flex-row grid grid-cols-3 lg:grid-cols-3 gap-4 items-center">
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "tp2be", "all")}
          className="text-xs w-full py-2 px-4 bg-cyan-600 text-white font-semibold rounded-md hover:bg-cyan-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "ALL TP > BE"}
        </button>
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "tp2be", "all-buy")}
          className="text-xs w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "BUY TP > BE"}
        </button>

        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "tp2be", "all-sell")}
          className="text-xs w-full py-2 px-4 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "SELL TP > BE"}
        </button>
      </div>


      <div className="w-full pt-3">
        <h3>
          TRAILING STOPLOSS
        </h3>
      </div>

      {/* Close all*/}
      <div className="flex flex-col sm:flex-row grid grid-cols-3 lg:grid-cols-3 gap-4 items-center">
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "trailing-sl", "all")}
          className="text-xs w-full py-2 px-4 bg-cyan-600 text-white font-semibold rounded-md hover:bg-cyan-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "ALL SL > BE"}
        </button>
        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "trailing-sl", "buy")}
          className="text-xs w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "BUY SL > BE"}
        </button>

        <button
          disabled={loading}
          onClick={() => handleSubmitClick(data.s, "trailing-sl", "sell")}
          className="text-xs w-full py-2 px-4 bg-red-600 text-white font-semibold rounded-md hover:bg-red-700 disabled:opacity-50"
        >
          {loading ? "⏳ กำลังส่ง..." : "SELL SL > BE"}
        </button>
      </div>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showConfirmation}
        title="Confirm Webhook Send"
        message={`Are you sure you want to send ${data.a} - ${data.c} signal for ${data.s}?`}
        confirmText="Send Webhook"
        cancelText="Cancel"
        onConfirm={handleConfirmSubmit}
        onCancel={handleCancelSubmit}
        type="warning"
        data={{
          ...data
          // action: pendingAction,
          // symbol: data.s,
          // comment: data.c
        }}
      />
    </div>
  );
}
