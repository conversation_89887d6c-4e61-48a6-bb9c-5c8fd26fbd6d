import { NextRequest, NextResponse } from 'next/server';
import { getWebhookConfigForPath, getPathFromReferer } from '@/utils/webhookConfig';
import { getConfigFilePathForPath, getConfigDirForPath, getPathFromHeaders } from '@/utils/pathConfig';

export async function GET(request: NextRequest) {
  try {
    // Get the current path from headers (set by middleware)
    // const refererPath = request.headers.get('x-current-path') || '/';
    const refererPath = getPathFromHeaders(request);
    const webhookConfig = await getWebhookConfigForPath(refererPath);

    const webhookUrl = `${webhookConfig.webhookHost}/webhook_orders_data`;

    if (!webhookUrl) {
      return NextResponse.json(
        { error: true, message: 'Webhook URL not configured' },
        { status: 500 }
      );
    }

    console.log(`Proxying webhook to get order: ${webhookUrl}`);
    console.log(`Using webhook config for path ${refererPath}:`, {
      host: webhookConfig.webhookHost,
      token: webhookConfig.webhookAccessToken ? '***' + webhookConfig.webhookAccessToken.slice(-4) : 'none'
    });

    // Get access token from webhook configuration (path-specific)
    const accessToken = webhookConfig.webhookAccessToken;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'User-Agent': 'NextJS-Webhook-Proxy/1.0',
    };

    // Add access token to headers if available
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
      headers['X-Access-Token'] = accessToken;
    }

    const response = await fetch(webhookUrl, {
      method: 'GET',
      headers
    });

    const responseText = await response.text();
    let responseData;

    // Log the raw response for debugging
    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));
    console.log(`Raw response:`, responseText.substring(0, 500)); // First 500 chars

    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = {
        message: responseText,
        isHtml: responseText.trim().startsWith('<!doctype') || responseText.trim().startsWith('<html')
      };
    }

    // If the webhook server returned an error status, mark as unsuccessful
    const success = response.ok && !responseText.includes('Internal Server Error');

    return NextResponse.json({
      success,
      status: response.status,
      statusText: response.statusText,
      data: responseData,
      webhookUrl, // Include for debugging
    });

  } catch (error: unknown) {
    console.error('Webhook proxy error:', error);
    return NextResponse.json(
      { 
        error: true, 
        message: (error as Error).message || 'Internal server error',
        details: String(error)
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Access-Token',
    },
  });
}
